﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32505.173
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "leechcore", "leechcore\leechcore.vcxproj", "{3476ABD2-5DEA-43E6-A676-8BE25F74535A}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "leechcorepyc", "leechcorepyc\leechcorepyc.vcxproj", "{1DD086DE-3BC9-458F-A2E1-F20142AA4977}"
	ProjectSection(ProjectDependencies) = postProject
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A} = {3476ABD2-5DEA-43E6-A676-8BE25F74535A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "leechagent", "leechagent\leechagent.vcxproj", "{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}"
	ProjectSection(ProjectDependencies) = postProject
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A} = {3476ABD2-5DEA-43E6-A676-8BE25F74535A}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "leechagent_linux", "leechagent_linux", "{51A6D505-4CE1-4F3A-8213-9E9B92FF21B7}"
	ProjectSection(SolutionItems) = preProject
		leechagent_linux\leechagent.c = leechagent_linux\leechagent.c
		leechagent_linux\leechagent.h = leechagent_linux\leechagent.h
		leechagent_linux\leechagent_linux.code-workspace = leechagent_linux\leechagent_linux.code-workspace
		leechagent_linux\leechagent_proc.h = leechagent_linux\leechagent_proc.h
		leechagent_linux\leechagent_rpc.c = leechagent_linux\leechagent_rpc.c
		leechagent_linux\leechagent_rpc.h = leechagent_linux\leechagent_rpc.h
		leechagent_linux\leechrpc.h = leechagent_linux\leechrpc.h
		leechagent_linux\leechrpcserver.c = leechagent_linux\leechrpcserver.c
		leechagent_linux\leechrpcshared.c = leechagent_linux\leechrpcshared.c
		leechagent_linux\Makefile = leechagent_linux\Makefile
		leechagent_linux\oscompatibility.c = leechagent_linux\oscompatibility.c
		leechagent_linux\oscompatibility.h = leechagent_linux\oscompatibility.h
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|ARM64.Build.0 = Debug|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x64.ActiveCfg = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x64.Build.0 = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x86.ActiveCfg = Debug|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x86.Build.0 = Debug|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|ARM64.ActiveCfg = Release|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|ARM64.Build.0 = Release|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x64.ActiveCfg = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x64.Build.0 = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x86.ActiveCfg = Release|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x86.Build.0 = Release|Win32
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Debug|ARM64.ActiveCfg = Debug|x64
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Debug|x64.ActiveCfg = Debug|x64
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Debug|x64.Build.0 = Debug|x64
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Debug|x86.ActiveCfg = Debug|Win32
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Release|ARM64.ActiveCfg = Release|x64
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Release|x64.ActiveCfg = Release|x64
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Release|x64.Build.0 = Release|x64
		{1DD086DE-3BC9-458F-A2E1-F20142AA4977}.Release|x86.ActiveCfg = Release|Win32
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Debug|ARM64.ActiveCfg = Debug|x64
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Debug|x64.ActiveCfg = Debug|x64
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Debug|x64.Build.0 = Debug|x64
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Debug|x86.ActiveCfg = Debug|Win32
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Debug|x86.Build.0 = Debug|Win32
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Release|ARM64.ActiveCfg = Release|x64
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Release|x64.ActiveCfg = Release|x64
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Release|x64.Build.0 = Release|x64
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Release|x86.ActiveCfg = Release|Win32
		{931BE9A5-0C7A-4D2F-8E37-7E3010A9979F}.Release|x86.Build.0 = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C56A097C-48F0-4688-BCF6-6B94DDE5C3B9}
	EndGlobalSection
EndGlobal
