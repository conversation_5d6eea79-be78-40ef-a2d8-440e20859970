﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\includes">
      <UniqueIdentifier>{81e2f737-13dd-4ab5-8d2d-f8f822c46c53}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ob">
      <UniqueIdentifier>{b9ed75de-f5b0-49ac-8690-6322fbe7421d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ob">
      <UniqueIdentifier>{d7ea25e4-3035-471b-9608-4fe0b262a79b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ob\ob_cachemap.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_core.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_map.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_set.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="vfslist.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="charutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="memprocfs_dokan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="memprocfs_fuse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oscompatibility.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\dokan.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\fileinfo.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\leechcore.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\public.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\vmmdll.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="ob\ob.h">
      <Filter>Header Files\ob</Filter>
    </ClInclude>
    <ClInclude Include="charutil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oscompatibility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vfslist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="MemProcFS.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Makefile.macos">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>