﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LocalDebuggerCommand>python.exe</LocalDebuggerCommand>
    <LocalDebuggerCommandArguments>$(SolutionDir)\files\test.py</LocalDebuggerCommandArguments>
    <DebuggerFlavor>WindowsRemoteDebugger</DebuggerFlavor>
    <RemoteDebuggerCommand>C:\Program Files\Python39\python.exe</RemoteDebuggerCommand>
    <RemoteDebuggerCommandArguments>\\ad.frizk.net\data\dev-pcileech\bin\test.py</RemoteDebuggerCommandArguments>
    <RemoteDebuggerServerName>WORKSTATION.ad.frizk.net</RemoteDebuggerServerName>
    <RemoteDebuggerWorkingDirectory>\\ad.frizk.net\data\dev-pcileech\bin\</RemoteDebuggerWorkingDirectory>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LocalDebuggerCommand>python.exe</LocalDebuggerCommand>
    <LocalDebuggerCommandArguments>$(SolutionDir)\files\test.py</LocalDebuggerCommandArguments>
    <DebuggerFlavor>WindowsRemoteDebugger</DebuggerFlavor>
    <RemoteDebuggerCommand>C:\Program Files\Python39\python.exe</RemoteDebuggerCommand>
    <RemoteDebuggerCommandArguments>\\ad.frizk.net\data\dev-pcileech\bin\test.py</RemoteDebuggerCommandArguments>
    <RemoteDebuggerServerName>WORKSTATION.ad.frizk.net</RemoteDebuggerServerName>
    <RemoteDebuggerWorkingDirectory>\\ad.frizk.net\data\dev-pcileech\bin\</RemoteDebuggerWorkingDirectory>
  </PropertyGroup>
</Project>