# MemProcFS ELK Importer Example:
#
# The MemProcFS ELK importer automatically imports forensic json data to a an
# ElasticSearch / Kibana running without password at localhost (default).
#
# It is possible to import multiple memory dumps in the same database.
#

# Set MemProcFS JSON ROOT to $PSScriptRoot by default, alternatively use the
# user supplied drive-letter in 1st script argument ($args[0]).
$MEMPROCFS_JSON_ROOT = $PSScriptRoot
if(($args.Count -eq 1) -and ($args[0].length -eq 1)) {
    $MEMPROCFS_JSON_ROOT = $args[0] + ":\forensic\json"
}

$ELK_CONFIG_NDJSON = '
{"attributes":{"fieldAttrs":"{}","fields":"[]","runtimeFieldMap":"{}","timeFieldName":"date","title":"mp_timeline"},"coreMigrationVersion":"7.12.0","id":"4412f450-9884-11eb-900a-8d859ec6b571","migrationVersion":{"index-pattern":"7.11.0"},"references":[],"type":"index-pattern","updated_at":"2021-04-23T06:10:30.992Z","version":"WzE2ODc0NSw0XQ=="}
{"attributes":{"fieldAttrs":"{}","fields":"[]","runtimeFieldMap":"{}","title":"mp_general"},"coreMigrationVersion":"7.12.0","id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","migrationVersion":{"index-pattern":"7.11.0"},"references":[],"type":"index-pattern","updated_at":"2021-04-23T06:10:50.958Z","version":"WzE2ODc1Myw0XQ=="}
{"attributes":{"fieldAttrs":"{}","fields":"[]","runtimeFieldMap":"{}","title":"mp_registry"},"coreMigrationVersion":"7.12.0","id":"5cac1500-9b81-11eb-900a-8d859ec6b571","migrationVersion":{"index-pattern":"7.11.0"},"references":[],"type":"index-pattern","updated_at":"2021-04-23T06:11:26.542Z","version":"WzE2ODc2Myw0XQ=="}
{"attributes":{"columns":["sys","type","pid","proc","i","obj","num","size","addr","addr2","hex","hex2","desc","desc2"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Discover_General","version":1},"coreMigrationVersion":"7.12.0","id":"362a3820-a52d-11eb-895a-0f8678ef2295","migrationVersion":{"search":"7.9.3"},"references":[{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"sort":[],"type":"search","updated_at":"2021-04-24T18:45:16.578Z","version":"WzEwNiwxXQ=="}
{"attributes":{"columns":["sys","type","action","pid","num","hex","desc"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","desc"]],"title":"MemProcFS_Discover_Timeline","version":1},"coreMigrationVersion":"7.12.0","id":"3ebdb1f0-a52e-11eb-895a-0f8678ef2295","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"sort":[],"type":"search","updated_at":"2021-04-24T18:52:40.463Z","version":"WzIwOSwxXQ=="}
{"attributes":{"columns":["sys","type","key","lastwrite","value.name","value.type","value.size","value.data"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Discover_Registry","version":1},"coreMigrationVersion":"7.12.0","id":"b9f6e950-a52d-11eb-895a-0f8678ef2295","migrationVersion":{"search":"7.9.3"},"references":[{"id":"5cac1500-9b81-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"sort":[],"type":"search","updated_at":"2021-04-24T18:48:57.701Z","version":"WzE1MSwxXQ=="}
{"attributes":{"description":null,"state":{"datasourceStates":{"indexpattern":{"layers":{"408237c9-7d7e-4b11-9857-7afec58a5494":{"columnOrder":["9415df44-805f-4cf7-a825-2e15be1d5dda","1fc14ce3-494e-4df3-b392-262c44b724e9"],"columns":{"1fc14ce3-494e-4df3-b392-262c44b724e9":{"dataType":"number","isBucketed":false,"label":"Unique count of desc.keyword","operationType":"cardinality","scale":"ratio","sourceField":"desc.keyword"},"9415df44-805f-4cf7-a825-2e15be1d5dda":{"dataType":"string","isBucketed":true,"label":"Top values of action.keyword","operationType":"terms","params":{"missingBucket":false,"orderBy":{"type":"alphabetical"},"orderDirection":"asc","otherBucket":true,"size":5},"scale":"ordinal","sourceField":"action.keyword"}},"incompleteColumns":{}}}}},"filters":[],"query":{"language":"kuery","query":""},"visualization":{"axisTitlesVisibilitySettings":{"x":true,"yLeft":true,"yRight":true},"fittingFunction":"None","gridlinesVisibilitySettings":{"x":true,"yLeft":true,"yRight":true},"layers":[{"accessors":["1fc14ce3-494e-4df3-b392-262c44b724e9"],"layerId":"408237c9-7d7e-4b11-9857-7afec58a5494","seriesType":"bar_stacked","xAccessor":"9415df44-805f-4cf7-a825-2e15be1d5dda"}],"legend":{"isVisible":true,"position":"right"},"preferredSeriesType":"bar_stacked","tickLabelsVisibilitySettings":{"x":true,"yLeft":true,"yRight":true},"valueLabels":"hide"}},"title":"MemProcFS_OperationsOverview","visualizationType":"lnsXY"},"coreMigrationVersion":"7.12.0","id":"b0b13720-98a2-11eb-900a-8d859ec6b571","migrationVersion":{"lens":"7.12.0"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"indexpattern-datasource-current-indexpattern","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"indexpattern-datasource-layer-408237c9-7d7e-4b11-9857-7afec58a5494","type":"index-pattern"}],"type":"lens","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwMSw0XQ=="}
{"attributes":{"description":"","state":{"datasourceStates":{"indexpattern":{"layers":{"408237c9-7d7e-4b11-9857-7afec58a5494":{"columnOrder":["858f5987-5934-4b5c-86e5-fbbf3abecbff","1fc14ce3-494e-4df3-b392-262c44b724e9"],"columns":{"1fc14ce3-494e-4df3-b392-262c44b724e9":{"dataType":"number","isBucketed":false,"label":"Unique count of desc.keyword","operationType":"cardinality","scale":"ratio","sourceField":"desc.keyword"},"858f5987-5934-4b5c-86e5-fbbf3abecbff":{"dataType":"string","isBucketed":true,"label":"Top values of type.keyword","operationType":"terms","params":{"missingBucket":false,"orderBy":{"columnId":"1fc14ce3-494e-4df3-b392-262c44b724e9","type":"column"},"orderDirection":"desc","otherBucket":true,"size":5},"scale":"ordinal","sourceField":"type.keyword"}},"incompleteColumns":{}}}}},"filters":[],"query":{"language":"kuery","query":""},"visualization":{"axisTitlesVisibilitySettings":{"x":true,"yLeft":true,"yRight":true},"fittingFunction":"None","gridlinesVisibilitySettings":{"x":true,"yLeft":true,"yRight":true},"layers":[{"accessors":["1fc14ce3-494e-4df3-b392-262c44b724e9"],"layerId":"408237c9-7d7e-4b11-9857-7afec58a5494","seriesType":"bar_stacked","xAccessor":"858f5987-5934-4b5c-86e5-fbbf3abecbff"}],"legend":{"isVisible":true,"position":"right"},"preferredSeriesType":"bar_stacked","tickLabelsVisibilitySettings":{"x":true,"yLeft":true,"yRight":true},"valueLabels":"hide"}},"title":"MemProcFS_OperationsOverviewByType","visualizationType":"lnsXY"},"coreMigrationVersion":"7.12.0","id":"f9bfe9c0-98a2-11eb-900a-8d859ec6b571","migrationVersion":{"lens":"7.12.0"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"indexpattern-datasource-current-indexpattern","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"indexpattern-datasource-layer-408237c9-7d7e-4b11-9857-7afec58a5494","type":"index-pattern"}],"type":"lens","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwMiw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"desc: (INetCache OR Recent OR Temp OR Prefetch OR TrustRecords OR PowerShell\\\\.evtx) OR type: (PROC OR Net)\",\"language\":\"lucene\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_ForensicQuickHuntTimeLine","version":1},"coreMigrationVersion":"7.12.0","id":"307cba20-989d-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwMyw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_ForensicQuickHuntTimeLineVisual","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_ForensicQuickHuntTimeLineVisual\",\"type\":\"horizontal_bar\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":30,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"segment\"}],\"params\":{\"type\":\"histogram\",\"grid\":{\"categoryLines\":false},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"left\",\"show\":true,\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":200},\"title\":{},\"style\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"bottom\",\"show\":true,\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":75,\"filter\":true,\"truncate\":100},\"title\":{\"text\":\"Count\"},\"style\":{}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"normal\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"interpolate\":\"linear\",\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"lineWidth\":2,\"showCircles\":true}],\"addTooltip\":true,\"detailedTooltip\":true,\"palette\":{\"type\":\"palette\",\"name\":\"default\"},\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false,\"labels\":{},\"radiusRatio\":0,\"thresholdLine\":{\"show\":false,\"value\":10,\"width\":1,\"style\":\"full\",\"color\":\"#E7664C\"}}}"},"coreMigrationVersion":"7.12.0","id":"9457f760-98a9-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"307cba20-989d-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwNCw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic","version":1},"coreMigrationVersion":"7.12.0","id":"ad10e510-9885-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwNSw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_Aggregation_Top30","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_Aggregation_Top30\",\"type\":\"horizontal_bar\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":30,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"segment\"}],\"params\":{\"type\":\"histogram\",\"grid\":{\"categoryLines\":false},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"left\",\"show\":true,\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":200},\"title\":{},\"style\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"bottom\",\"show\":true,\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":75,\"filter\":true,\"truncate\":100},\"title\":{\"text\":\"Count\"},\"style\":{}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"normal\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"interpolate\":\"linear\",\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"lineWidth\":2,\"showCircles\":true}],\"addTooltip\":true,\"detailedTooltip\":true,\"palette\":{\"type\":\"palette\",\"name\":\"default\"},\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false,\"labels\":{},\"radiusRatio\":0,\"thresholdLine\":{\"show\":false,\"value\":10,\"width\":1,\"style\":\"full\",\"color\":\"#E7664C\"}}}"},"coreMigrationVersion":"7.12.0","id":"809968f0-98a7-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"ad10e510-9885-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwNiw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_AggregationAll","uiStateJSON":"{\"vis\":{\"params\":{\"colWidth\":[{\"colIndex\":0,\"width\":1089}]}}}","version":1,"visState":"{\"title\":\"MemProcFS_AggregationAll\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"}}"},"coreMigrationVersion":"7.12.0","id":"1c5ec540-98a9-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"ad10e510-9885-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwNyw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"PROC\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"PROC\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_PROC","version":1},"coreMigrationVersion":"7.12.0","id":"1913f4f0-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwOCw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_AggregationBinarysDesc","uiStateJSON":"{\"vis\":{\"legendOpen\":true}}","version":1,"visState":"{\"title\":\"MemProcFS_AggregationBinarysDesc\",\"type\":\"horizontal_bar\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":20,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"segment\"}],\"params\":{\"type\":\"histogram\",\"grid\":{\"categoryLines\":false},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"left\",\"show\":true,\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":200},\"title\":{},\"style\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"bottom\",\"show\":true,\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":75,\"filter\":true,\"truncate\":100},\"title\":{\"text\":\"Count\"},\"style\":{}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"normal\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"interpolate\":\"linear\",\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"lineWidth\":2,\"showCircles\":true}],\"addTooltip\":true,\"detailedTooltip\":true,\"palette\":{\"type\":\"palette\",\"name\":\"default\"},\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false,\"labels\":{},\"radiusRatio\":0,\"thresholdLine\":{\"show\":false,\"value\":10,\"width\":1,\"style\":\"full\",\"color\":\"#E7664C\"}}}"},"coreMigrationVersion":"7.12.0","id":"3353ae40-98a5-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"1913f4f0-9886-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYwOSw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_AggregationBinarys","uiStateJSON":"{\"vis\":{\"params\":{\"colWidth\":[{\"colIndex\":0,\"width\":929}],\"sort\":{\"columnIndex\":1,\"direction\":\"desc\"}}}}","version":1,"visState":"{\"title\":\"MemProcFS_AggregationBinarys\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"}}"},"coreMigrationVersion":"7.12.0","id":"2c4e9ca0-98a4-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"1913f4f0-9886-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxMCw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"NTFS\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"NTFS\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_NTFS","version":1},"coreMigrationVersion":"7.12.0","id":"0d6f7520-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxMSw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"NET\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"NET\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_NET","version":1},"coreMigrationVersion":"7.12.0","id":"383fff90-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxMiw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"THREAD\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"THREAD\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_THREAD","version":1},"coreMigrationVersion":"7.12.0","id":"24726b10-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxMyw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"REG\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"REG\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_REG","version":1},"coreMigrationVersion":"7.12.0","id":"2dc95160-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxNCw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"ShTask\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"ShTask\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_ShTask","version":1},"coreMigrationVersion":"7.12.0","id":"42fae2b0-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxNSw0XQ=="}
{"attributes":{"columns":["type","action","num","pid","desc","hex","sys"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"KObj\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"KObj\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[["date","asc"]],"title":"MemProcFS_Forensic_KObj","version":1},"coreMigrationVersion":"7.12.0","id":"4da5e750-9886-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"4412f450-9884-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxNiw0XQ=="}
{"attributes":{"columns":["sys","type","pid","desc","obj","addr","addr2","size","num","hex","hex2","desc2"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Forensic_General","version":1},"coreMigrationVersion":"7.12.0","id":"7c445980-9b82-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYxOCw0XQ=="}
{"attributes":{"columns":["sys","type","key","value.type","value.name","value.size","value.data"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Forensic_Registry","version":1},"coreMigrationVersion":"7.12.0","id":"2dca06b0-9b91-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"5cac1500-9b81-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyMCw0XQ=="}
{"attributes":{"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"lucene\",\"query\":\"\"},\"filter\":[]}"},"optionsJSON":"{\"hidePanelTitles\":false,\"useMargins\":true}","panelsJSON":"[{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"71d83ce2-a410-4733-a3fd-c39388aa8660\"},\"panelIndex\":\"71d83ce2-a410-4733-a3fd-c39388aa8660\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_0\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"71f92449-6dd5-40cd-9783-8ddd3404015e\"},\"panelIndex\":\"71f92449-6dd5-40cd-9783-8ddd3404015e\",\"embeddableConfig\":{\"enhancements\":{},\"hidePanelTitles\":false},\"panelRefName\":\"panel_1\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":15,\"w\":48,\"h\":14,\"i\":\"31a65707-d0e2-4d62-b6f9-590d8663ba8a\"},\"panelIndex\":\"31a65707-d0e2-4d62-b6f9-590d8663ba8a\",\"embeddableConfig\":{\"vis\":{\"legendOpen\":false},\"enhancements\":{}},\"panelRefName\":\"panel_2\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":29,\"w\":48,\"h\":15,\"i\":\"756268c6-67f8-4608-a35e-ffc409f3c6d9\"},\"panelIndex\":\"756268c6-67f8-4608-a35e-ffc409f3c6d9\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_3\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":44,\"w\":48,\"h\":15,\"i\":\"0294030f-90a9-4dbc-8b00-18fb0acb5893\"},\"panelIndex\":\"0294030f-90a9-4dbc-8b00-18fb0acb5893\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_4\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":59,\"w\":48,\"h\":15,\"i\":\"4140dca2-d8b6-4c00-99d0-c3196aa2a5a8\"},\"panelIndex\":\"4140dca2-d8b6-4c00-99d0-c3196aa2a5a8\",\"embeddableConfig\":{\"vis\":{\"legendOpen\":false},\"enhancements\":{}},\"panelRefName\":\"panel_5\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":74,\"w\":48,\"h\":16,\"i\":\"59c26ad1-b39a-4e0c-8864-38eff1c292b2\"},\"panelIndex\":\"59c26ad1-b39a-4e0c-8864-38eff1c292b2\",\"embeddableConfig\":{\"vis\":{\"params\":{\"colWidth\":[{\"colIndex\":0,\"width\":1611}]}},\"enhancements\":{}},\"panelRefName\":\"panel_6\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":90,\"w\":48,\"h\":13,\"i\":\"83c8ebdc-8d81-453e-9eb1-33ff1845e5e8\"},\"panelIndex\":\"83c8ebdc-8d81-453e-9eb1-33ff1845e5e8\",\"embeddableConfig\":{\"vis\":{\"legendOpen\":false},\"enhancements\":{}},\"panelRefName\":\"panel_7\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":103,\"w\":48,\"h\":14,\"i\":\"24cd17c8-afa8-4178-bbc3-ddcdb6b08116\"},\"panelIndex\":\"24cd17c8-afa8-4178-bbc3-ddcdb6b08116\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_8\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":117,\"w\":48,\"h\":15,\"i\":\"7ab07e42-2336-40f5-a098-7e4203b937e7\"},\"panelIndex\":\"7ab07e42-2336-40f5-a098-7e4203b937e7\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_9\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":132,\"w\":48,\"h\":15,\"i\":\"df6cbb8c-46d3-4872-b2e2-751b2e527fee\"},\"panelIndex\":\"df6cbb8c-46d3-4872-b2e2-751b2e527fee\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_10\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":147,\"w\":24,\"h\":15,\"i\":\"a54657c6-212a-46ef-b94e-b9f7252b06c6\"},\"panelIndex\":\"a54657c6-212a-46ef-b94e-b9f7252b06c6\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_11\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":24,\"y\":147,\"w\":24,\"h\":15,\"i\":\"a84e351e-3d80-407e-b68c-b3178b91ca9f\"},\"panelIndex\":\"a84e351e-3d80-407e-b68c-b3178b91ca9f\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_12\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":162,\"w\":24,\"h\":15,\"i\":\"c34cf759-d34d-441f-b3fd-ea719792b446\"},\"panelIndex\":\"c34cf759-d34d-441f-b3fd-ea719792b446\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_13\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":24,\"y\":162,\"w\":24,\"h\":15,\"i\":\"eb650fc1-5649-44e8-b174-b547e209221e\"},\"panelIndex\":\"eb650fc1-5649-44e8-b174-b547e209221e\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_14\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":177,\"w\":48,\"h\":16,\"i\":\"98900dcf-e4f8-412d-844d-a6c18cbfe966\"},\"panelIndex\":\"98900dcf-e4f8-412d-844d-a6c18cbfe966\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_15\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":193,\"w\":48,\"h\":15,\"i\":\"2b0cbf20-29fe-4480-b557-b2af6c4c88cc\"},\"panelIndex\":\"2b0cbf20-29fe-4480-b557-b2af6c4c88cc\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_16\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":208,\"w\":48,\"h\":16,\"i\":\"0077f7a9-04ec-4a8c-9099-6cb0b5674c11\"},\"panelIndex\":\"0077f7a9-04ec-4a8c-9099-6cb0b5674c11\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_17\"}]","timeRestore":false,"title":"MemProcFS_ForensicHunting","version":1},"coreMigrationVersion":"7.12.0","id":"db207340-9898-11eb-900a-8d859ec6b571","migrationVersion":{"dashboard":"7.11.0"},"references":[{"id":"b0b13720-98a2-11eb-900a-8d859ec6b571","name":"panel_0","type":"lens"},{"id":"f9bfe9c0-98a2-11eb-900a-8d859ec6b571","name":"panel_1","type":"lens"},{"id":"9457f760-98a9-11eb-900a-8d859ec6b571","name":"panel_2","type":"visualization"},{"id":"307cba20-989d-11eb-900a-8d859ec6b571","name":"panel_3","type":"search"},{"id":"ad10e510-9885-11eb-900a-8d859ec6b571","name":"panel_4","type":"search"},{"id":"809968f0-98a7-11eb-900a-8d859ec6b571","name":"panel_5","type":"visualization"},{"id":"1c5ec540-98a9-11eb-900a-8d859ec6b571","name":"panel_6","type":"visualization"},{"id":"3353ae40-98a5-11eb-900a-8d859ec6b571","name":"panel_7","type":"visualization"},{"id":"2c4e9ca0-98a4-11eb-900a-8d859ec6b571","name":"panel_8","type":"visualization"},{"id":"1913f4f0-9886-11eb-900a-8d859ec6b571","name":"panel_9","type":"search"},{"id":"0d6f7520-9886-11eb-900a-8d859ec6b571","name":"panel_10","type":"search"},{"id":"383fff90-9886-11eb-900a-8d859ec6b571","name":"panel_11","type":"search"},{"id":"24726b10-9886-11eb-900a-8d859ec6b571","name":"panel_12","type":"search"},{"id":"2dc95160-9886-11eb-900a-8d859ec6b571","name":"panel_13","type":"search"},{"id":"42fae2b0-9886-11eb-900a-8d859ec6b571","name":"panel_14","type":"search"},{"id":"4da5e750-9886-11eb-900a-8d859ec6b571","name":"panel_15","type":"search"},{"id":"7c445980-9b82-11eb-900a-8d859ec6b571","name":"panel_16","type":"search"},{"id":"2dca06b0-9b91-11eb-900a-8d859ec6b571","name":"panel_17","type":"search"}],"type":"dashboard","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyMSw0XQ=="}
{"attributes":{"columns":["sys","type","pid","proc","desc","obj","addr","addr2","size","num","hex","hex2","desc2"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"process\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"process\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Forensic_Processname","version":1},"coreMigrationVersion":"7.12.0","id":"ea455940-9ba3-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyMiw0XQ=="}
{"attributes":{"columns":["sys","type","pid","proc","desc","obj","addr","addr2","size","num","hex","hex2","desc2"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"unloadedmodule\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"unloadedmodule\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Forensic_Unloadmodule","version":1},"coreMigrationVersion":"7.12.0","id":"6e3cf360-9b96-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyMyw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_Unloadedmodule_Desc","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_Unloadedmodule_Desc\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"}}"},"coreMigrationVersion":"7.12.0","id":"bf29b280-9b97-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"6e3cf360-9b96-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyNCw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_Unloadedmodule_PID","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_Unloadedmodule_PID\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"pid\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"}}"},"coreMigrationVersion":"7.12.0","id":"1edf5860-9b98-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"6e3cf360-9b96-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyNSw0XQ=="}
{"attributes":{"columns":["sys","type","pid","desc","obj","addr","addr2","size","num","hex","hex2","desc2"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"module\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"module\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Forensic_Module","version":1},"coreMigrationVersion":"7.12.0","id":"66ac1fb0-9b99-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyNiw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_Forensic_Modules","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_Forensic_Modules\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"proc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\",\"row\":false}}"},"coreMigrationVersion":"7.12.0","id":"c2c787d0-9b99-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"66ac1fb0-9b99-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyNyw0XQ=="}
{"attributes":{"columns":["sys","type","pid","desc","obj","addr","addr2","size","num","hex","hex2","desc2"],"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":false,\"disabled\":false,\"type\":\"phrase\",\"key\":\"type\",\"params\":{\"query\":\"evil\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match_phrase\":{\"type\":\"evil\"}},\"$state\":{\"store\":\"appState\"}},{\"meta\":{\"alias\":null,\"negate\":true,\"disabled\":false,\"type\":\"phrase\",\"key\":\"desc\",\"params\":{\"query\":\"PE_NOLINK  \"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index\"},\"query\":{\"match_phrase\":{\"desc\":\"PE_NOLINK  \"}},\"$state\":{\"store\":\"appState\"}},{\"meta\":{\"alias\":null,\"negate\":true,\"disabled\":false,\"type\":\"phrase\",\"key\":\"desc\",\"params\":{\"query\":\"PE_PATCHED \"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[2].meta.index\"},\"query\":{\"match_phrase\":{\"desc\":\"PE_PATCHED \"}},\"$state\":{\"store\":\"appState\"}},{\"meta\":{\"alias\":null,\"negate\":true,\"disabled\":false,\"type\":\"phrase\",\"key\":\"desc\",\"params\":{\"query\":\"PEB_BAD_LDR\"},\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[3].meta.index\"},\"query\":{\"match_phrase\":{\"desc\":\"PEB_BAD_LDR\"}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"sort":[],"title":"MemProcFS_Forensic_FindEvil","version":1},"coreMigrationVersion":"7.12.0","id":"0e4a47b0-9b9a-11eb-900a-8d859ec6b571","migrationVersion":{"search":"7.9.3"},"references":[{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[2].meta.index","type":"index-pattern"},{"id":"acc9fd50-9b80-11eb-900a-8d859ec6b571","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[3].meta.index","type":"index-pattern"}],"type":"search","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyOCw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_Forensic_FindEvil","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_Forensic_FindEvil\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"pid\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":1,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"},{\"id\":\"4\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"proc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"}}"},"coreMigrationVersion":"7.12.0","id":"0e805660-9b9b-11eb-900a-8d859ec6b571","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"0e4a47b0-9b9a-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYyOSw0XQ=="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]}"},"savedSearchRefName":"search_0","title":"MemProcFS_Forensic_PROC","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"MemProcFS_Forensic_PROC\",\"type\":\"table\",\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"pid\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":1,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"}}"},"coreMigrationVersion":"7.12.0","id":"67226440-a3b4-11eb-ab24-5dbd4da3470c","migrationVersion":{"visualization":"7.12.0"},"references":[{"id":"ea455940-9ba3-11eb-900a-8d859ec6b571","name":"search_0","type":"search"}],"type":"visualization","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYzMCw0XQ=="}
{"attributes":{"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[]}"},"optionsJSON":"{\"hidePanelTitles\":false,\"useMargins\":true}","panelsJSON":"[{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"19af6e34-72c1-4725-9418-66f44364e001\"},\"panelIndex\":\"19af6e34-72c1-4725-9418-66f44364e001\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_0\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"e5f86f51-8322-4135-ab7f-75b33dc549dd\"},\"panelIndex\":\"e5f86f51-8322-4135-ab7f-75b33dc549dd\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_1\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"0ad29a40-c5b8-4a06-9b88-fb6be0b45ebb\"},\"panelIndex\":\"0ad29a40-c5b8-4a06-9b88-fb6be0b45ebb\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_2\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"434de7bf-8b59-484e-b567-8e6f7f235435\"},\"panelIndex\":\"434de7bf-8b59-484e-b567-8e6f7f235435\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_3\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":0,\"y\":30,\"w\":24,\"h\":15,\"i\":\"6fb16d83-906c-4cd2-809b-ccd8ce4ca448\"},\"panelIndex\":\"6fb16d83-906c-4cd2-809b-ccd8ce4ca448\",\"embeddableConfig\":{\"enhancements\":{}},\"panelRefName\":\"panel_4\"},{\"version\":\"7.12.0\",\"gridData\":{\"x\":24,\"y\":30,\"w\":24,\"h\":15,\"i\":\"21f7112d-b622-4ac5-a559-e06ed0fc6aec\"},\"panelIndex\":\"21f7112d-b622-4ac5-a559-e06ed0fc6aec\",\"embeddableConfig\":{\"savedVis\":{\"title\":\"forensic_general_processname_vis\",\"description\":\"\",\"type\":\"table\",\"params\":{\"perPage\":500,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"showTotal\":false,\"showToolbar\":false,\"totalFunc\":\"sum\",\"percentageCol\":\"\"},\"uiState\":{},\"data\":{\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"params\":{},\"schema\":\"metric\"},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"desc.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":500,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"params\":{\"field\":\"pid.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":1,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"},\"schema\":\"bucket\"}],\"searchSource\":{\"query\":{\"query\":\"\",\"language\":\"lucene\"},\"filter\":[]},\"savedSearchId\":\"ea455940-9ba3-11eb-900a-8d859ec6b571\"}},\"enhancements\":{},\"vis\":{\"params\":{\"colWidth\":[{\"colIndex\":0,\"width\":750.6666666666667}],\"sort\":{\"columnIndex\":null,\"direction\":null}}},\"table\":null},\"panelRefName\":\"panel_5\"}]","timeRestore":false,"title":"MemProcFS_Malware_Hunt","version":1},"coreMigrationVersion":"7.12.0","id":"6520ba30-9b98-11eb-900a-8d859ec6b571","migrationVersion":{"dashboard":"7.11.0"},"references":[{"id":"ea455940-9ba3-11eb-900a-8d859ec6b571","name":"search_0","type":"search"},{"id":"6e3cf360-9b96-11eb-900a-8d859ec6b571","name":"panel_0","type":"search"},{"id":"bf29b280-9b97-11eb-900a-8d859ec6b571","name":"panel_1","type":"visualization"},{"id":"1edf5860-9b98-11eb-900a-8d859ec6b571","name":"panel_2","type":"visualization"},{"id":"c2c787d0-9b99-11eb-900a-8d859ec6b571","name":"panel_3","type":"visualization"},{"id":"0e805660-9b9b-11eb-900a-8d859ec6b571","name":"panel_4","type":"visualization"},{"id":"67226440-a3b4-11eb-ab24-5dbd4da3470c","name":"panel_5","type":"visualization"}],"type":"dashboard","updated_at":"2021-04-22T21:54:20.748Z","version":"WzE2ODYzMSw0XQ=="}
{"exportedCount":35,"missingRefCount":0,"missingReferences":[]}'


#----------------------------------------------------------------------------------------------------------------------------
# MemProcFS_ELK_Manual_Bulk_Import function is based on MIT-licensed work from:
# https://github.com/gorkemcnr/elasticsearch-powershell-bulkimport/blob/master/BulkElasticSearchImport.psm1
#----------------------------------------------------------------------------------------------------------------------------

function MemProcFS_ELK_Manual_Bulk_Import([string]$filePath, [int]$maxLineCount, [string]$url, [string]$username, [string]$password)
{
    if(!$url) {
        Write-host -ForegroundColor Yellow '[*] You Need To Specify: MemProcFS_ELK_Manual_Bulk_Import "general.json" 10000 "http://localhost:9200/mp_general/_bulk"'
        return
    }

    Add-Type -AssemblyName System.Net.Http
    $httpClient = New-Object System.Net.Http.Httpclient
    $method =  New-Object System.Net.Http.HttpMethod("POST")
    $contentType = New-Object System.Net.Http.Headers.MediaTypeHeaderValue("application/x-ndjson");

    function BulkRequest([string]$url, [System.Text.StringBuilder]$bulkIndexLines) {	
	    $message = New-Object System.Net.Http.HttpRequestMessage($method, $url)
	    $message.Content = New-Object System.Net.Http.StringContent($bulkIndexLines.ToString())
	    $message.Content.Headers.Clear()
	    $message.Content.Headers.ContentType = $contentType 
	    #Write-host $message
	    $response = $httpClient.SendAsync($message).Result
	    #Write-host $response
	    [void]$response.EnsureSuccessStatusCode()
    }

	$encodedCredentials = [System.Convert]::ToBase64String([System.Text.Encoding]::ASCII.GetBytes($username + ":" + $password))	 	
	$httpClient.DefaultRequestHeaders.Authorization = New-Object System.Net.Http.Headers.AuthenticationHeaderValue("Basic", $encodedCredentials);
    $url = [Uri]::new([Uri]::new($url)).ToString() 
	$newLine = "`r`n" 	
	$indexInfo = "{""index"":{}}" + $newLine 	
	$lineCount = 0
	$bulkIndexLines = [System.Text.StringBuilder]::new()
	$file = New-Object System.IO.StreamReader -Arg $filePath

	try {
		Write-Host "[*] Import Process started on $(Get-Date)" -ForegroundColor Yellow -BackgroundColor Black
		while ($line = $file.ReadLine()) {
			[void]$bulkIndexLines.AppendLine($indexInfo + $line)
			$lineCounts++
			if(($lineCounts % $maxLineCount) -eq 0) {			
				BulkRequest $url $bulkIndexLines
				Write-Host "`r$lineCounts lines have processed" -NoNewline -ForegroundColor White -BackgroundColor Black			
				[void]$bulkIndexLines.Clear()
			}	  	
		}
		if($bulkIndexLines.Length -gt 0) {
			BulkRequest $url $bulkIndexLines		
			Write-Host "`r$lineCounts lines have processed" -NoNewline -ForegroundColor White -BackgroundColor Black			
			[void]$bulkIndexLines.Clear()
		}
		Write-Host $newLine"[*] Import Process has completed successfully on $(Get-Date)" -ForegroundColor Green -BackgroundColor Black		
	} catch {		
		Write-Host $newLine"[*] Import Process failed on $(Get-Date)" -ForegroundColor Red -BackgroundColor Black
		Write-Host "Exception: $_.Exception.Message" -ForegroundColor Red -BackgroundColor Black    	
	} finally {
		[void]$bulkIndexLines.Clear()
		$file.close()
	}
}

function MemProcFS_ELK_ImportIndex([string]$sysid, [string]$indexName, [string]$filePath, [string]$username, [string]$password)
{
    # 1: verify / create index
    try {
        $rest_result = Invoke-RestMethod -uri http://localhost:9200/$indexName/_search?q=sys.keyword:$sysid
        if($rest_result.hits.total.value -gt 0) {
            Write-Host -ForegroundColor Yellow "[*] Index: $indexName System: $sysid Previously Loaded"
            return
        }
    } catch {}
    Write-Host -ForegroundColor Yellow "[*] Missing Index: $indexName System: $sysid"
    Write-Host -ForegroundColor Yellow "[*] Creating Index: $indexName System: $sysid"
    MemProcFS_ELK_Manual_Bulk_Import $filePath 10000 "http://localhost:9200/$indexName/_bulk" $username $password
}


function MemProcFS_ELKImport()
{
    #--------------------------------------------------------------------------
    # RETRIEVE MEMPROCFS INFO
    #--------------------------------------------------------------------------
    $sysid = Get-Content -Path $MEMPROCFS_JSON_ROOT\..\..\sys\unique-tag.txt

    #--------------------------------------------------------------------------
    # VERIFY ELASTICSEARCH AND KIBANA AVAILABILITY
    #--------------------------------------------------------------------------
    try {
        $status = Invoke-WebRequest -Uri http://localhost:9200/ -UseBasicParsing
    } catch {
        Write-Host -ForegroundColor Red "[*] Unable to connect to ElasticSearch at http://localhost:9200/"
        break
    }
    try {
        $status = Invoke-WebRequest -Uri http://localhost:5601/ -UseBasicParsing
    } catch {
        Write-Host -ForegroundColor Red "[*] Unable to connect to Kibana at http://localhost:5601/"
        break
    }

    #--------------------------------------------------------------------------
    # CHECK HUNTING DASHBOARDS - LOAD FROM .ndjson IF MISSING!
    #--------------------------------------------------------------------------
    try {
        Write-Host -ForegroundColor Yellow "[*] Checking Hunting Dashboards Loaded in Kibana"
        $request_forensichunting = Invoke-RestMethod -Uri http://localhost:5601/api/kibana/dashboards/export?dashboard=db207340-9898-11eb-900a-8d859ec6b571 -UseBasicParsing
        if($request_forensichunting.objects[0].error.statusCode -eq 404) {
            throw "No Hunting Dashboards Loaded in Kibana"
        }
        Write-Host -ForegroundColor Yellow "[*] Hunting Dashboards Loaded"
    } catch {
        Write-Host -ForegroundColor Yellow "[*] No Hunting Dashboards Loaded in Kibana"
        Write-Host -ForegroundColor Yellow "[*] Installing Hunting Dashboards"
        $temp_file = New-TemporaryFile
        $temp_file_ndjson = [string]$temp_file + '.ndjson'
        Rename-Item -Path $temp_file -NewName $temp_file_ndjson
        Set-Content -Path $temp_file_ndjson -Value $ELK_CONFIG_NDJSON
        $run = 'curl.exe -X POST http://localhost:5601/api/saved_objects/_import?overwrite=true -H "kbn-xsrf: true" --form file="@' + $temp_file_ndjson + '"'
        Write-Host -ForegroundColor Green $run
        $result = Invoke-Expression $run 2>&1
        Remove-Item -Path $temp_file_ndjson
        #if($result) { Write-Host -ForegroundColor Red "[*]" $result break }
    }

    #--------------------------------------------------------------------------
    # IMPORT DATA
    #--------------------------------------------------------------------------
    $jsonFilePath_general = $MEMPROCFS_JSON_ROOT + "\general.json"
    $jsonFilePath_timeline = $MEMPROCFS_JSON_ROOT + "\timeline.json"
    $jsonFilePath_registry = $MEMPROCFS_JSON_ROOT + "\registry.json"
    MemProcFS_ELK_ImportIndex $sysid "mp_general" $jsonFilePath_general
    MemProcFS_ELK_ImportIndex $sysid "mp_timeline" $jsonFilePath_timeline
    MemProcFS_ELK_ImportIndex $sysid "mp_registry" $jsonFilePath_registry

    Write-Host -ForegroundColor Green "[*] ELK Import Finished! To search visit: http://localhost:5601"
}

MemProcFS_ELKImport
Exit 0