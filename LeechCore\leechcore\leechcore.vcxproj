﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <ProjectGuid>{3476ABD2-5DEA-43E6-A676-8BE25F74535A}</ProjectGuid>
    <RootNamespace>LeechCore</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
    <EnableASAN>false</EnableASAN>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
    <EnableASAN>false</EnableASAN>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)files\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)files\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
      <PreprocessorDefinitions>LEECHCORE_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Credui.lib;rpcrt4.lib;Secur32.lib;setupapi.lib;winusb.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>$(OutDir)\lib\$(TargetName).lib</ImportLibrary>
    </Link>
    <PostBuildEvent>
      <Command>if not exist "$(SolutionDir)\includes\lib64" mkdir "$(SolutionDir)\includes\lib64"
copy "$(OutDir)\lib\leechcore.lib" "$(SolutionDir)\includes\lib64\" /y
copy "$(ProjectDir)\leechcore.h" "$(SolutionDir)\includes\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>Stub</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>None</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
      <PreprocessorDefinitions>LEECHCORE_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Credui.lib;rpcrt4.lib;Secur32.lib;setupapi.lib;winusb.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>$(OutDir)\lib\$(TargetName).lib</ImportLibrary>
    </Link>
    <PostBuildEvent>
      <Command>if not exist "$(SolutionDir)\includes\lib32" mkdir "$(SolutionDir)\includes\lib32"
copy "$(OutDir)\lib\leechcore.lib" "$(SolutionDir)\includes\lib32\" /y
copy "$(ProjectDir)\leechcore.h" "$(SolutionDir)\includes\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>Stub</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>None</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
      <PreprocessorDefinitions>LEECHCORE_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Credui.lib;rpcrt4.lib;Secur32.lib;setupapi.lib;winusb.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>$(OutDir)\lib\$(TargetName).lib</ImportLibrary>
    </Link>
    <PostBuildEvent>
      <Command>if not exist "$(SolutionDir)\includes\libarm64" mkdir "$(SolutionDir)\includes\libarm64"
copy "$(OutDir)\lib\leechcore.lib" "$(SolutionDir)\includes\libarm64\" /y

copy "$(ProjectDir)\leechcore.h" "$(SolutionDir)\includes\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>Stub</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>None</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <PreprocessorDefinitions>LEECHCORE_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>Credui.lib;rpcrt4.lib;Secur32.lib;setupapi.lib;winusb.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>$(OutDir)\lib\$(TargetName).lib</ImportLibrary>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <CETCompat>true</CETCompat>
    </Link>
    <PostBuildEvent>
      <Command>if not exist "$(SolutionDir)\includes\lib64" mkdir "$(SolutionDir)\includes\lib64"
copy "$(OutDir)\lib\leechcore.lib" "$(SolutionDir)\includes\lib64\" /y
copy "$(ProjectDir)\leechcore.h" "$(SolutionDir)\includes\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>Stub</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>None</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PreprocessorDefinitions>LEECHCORE_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>Credui.lib;rpcrt4.lib;Secur32.lib;setupapi.lib;winusb.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>$(OutDir)\lib\$(TargetName).lib</ImportLibrary>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
    </Link>
    <PostBuildEvent>
      <Command>if not exist "$(SolutionDir)\includes\lib32" mkdir "$(SolutionDir)\includes\lib32"
copy "$(OutDir)\lib\leechcore.lib" "$(SolutionDir)\includes\lib32\" /y
copy "$(ProjectDir)\leechcore.h" "$(SolutionDir)\includes\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>Stub</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>None</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <CompileAs>CompileAsC</CompileAs>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PreprocessorDefinitions>LEECHCORE_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>Credui.lib;rpcrt4.lib;Secur32.lib;setupapi.lib;winusb.lib;ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>$(OutDir)\lib\$(TargetName).lib</ImportLibrary>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
    </Link>
    <PostBuildEvent>
      <Command>if not exist "$(SolutionDir)\includes\libarm64" mkdir "$(SolutionDir)\includes\libarm64"
copy "$(OutDir)\lib\leechcore.lib" "$(SolutionDir)\includes\libarm64\" /y

copy "$(ProjectDir)\leechcore.h" "$(SolutionDir)\includes\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Midl>
      <GenerateClientFiles>Stub</GenerateClientFiles>
    </Midl>
    <Midl>
      <GenerateServerFiles>None</GenerateServerFiles>
      <AdditionalOptions>/robust %(AdditionalOptions)</AdditionalOptions>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="device_file.c" />
    <ClCompile Include="device_fpga.c" />
    <ClCompile Include="device_hibr.c" />
    <ClCompile Include="device_pmem.c" />
    <ClCompile Include="device_tmd.c" />
    <ClCompile Include="device_usb3380.c" />
    <ClCompile Include="device_vmm.c" />
    <ClCompile Include="device_vmware.c" />
    <ClCompile Include="leechcore.c" />
    <ClCompile Include="leechrpcshared.c" />
    <ClCompile Include="leechrpcclient.c" />
    <ClCompile Include="leechrpc_c.c" />
    <ClCompile Include="memmap.c" />
    <ClCompile Include="ob\ob_bytequeue.c" />
    <ClCompile Include="ob\ob_core.c" />
    <ClCompile Include="ob\ob_map.c" />
    <ClCompile Include="ob\ob_set.c" />
    <ClCompile Include="oscompatibility.c" />
    <ClCompile Include="util.c">
      <ExcludedFromBuild>false</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="leechcore.h" />
    <ClInclude Include="leechcore_device.h" />
    <ClInclude Include="leechcore_internal.h" />
    <ClInclude Include="leechrpc.h" />
    <ClInclude Include="leechrpc_h.h" />
    <ClInclude Include="ob\ob.h" />
    <ClInclude Include="oscompatibility.h" />
    <ClInclude Include="util.h" />
    <ClInclude Include="version.h" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="leechrpc.idl" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="leechcore.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
    <None Include="Makefile.macos" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>