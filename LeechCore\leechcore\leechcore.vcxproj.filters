﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\ob">
      <UniqueIdentifier>{0d7968b8-c699-4099-a783-9482426d0802}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ob">
      <UniqueIdentifier>{b6ad61ff-a1bb-4908-90dc-116c7af6e5c1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="device_file.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="device_fpga.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="device_pmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="device_tmd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="device_usb3380.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechcore.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechrpc_c.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechrpcclient.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="memmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oscompatibility.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechrpcshared.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="device_vmware.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="device_vmm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_core.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_map.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_set.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_bytequeue.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="device_hibr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="leechrpc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechrpc_h.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oscompatibility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechcore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechcore_device.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechcore_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ob\ob.h">
      <Filter>Header Files\ob</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Midl Include="leechrpc.idl">
      <Filter>Resource Files</Filter>
    </Midl>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="leechcore.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Makefile.macos">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>