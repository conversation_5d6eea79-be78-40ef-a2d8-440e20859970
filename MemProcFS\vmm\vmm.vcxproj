<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <ProjectGuid>{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>vmm</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <SpectreMitigation>false</SpectreMitigation>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)files\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\</IntDir>
    <ExecutablePath>$(VC_ExecutablePath_x64);$(WindowsSDK_ExecutablePath);$(VS_ExecutablePath);$(MSBuild_ExecutablePath);$(FxCopDir);$(PATH)</ExecutablePath>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(SolutionDir)includes\lib64;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ExecutablePath>$(VC_ExecutablePath_x86);$(WindowsSDK_ExecutablePath);$(VS_ExecutablePath);$(MSBuild_ExecutablePath);$(FxCopDir);$(PATH)</ExecutablePath>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(SolutionDir)includes\lib32;</LibraryPath>
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <ExecutablePath>$(VC_ExecutablePath_x86);$(WindowsSDK_ExecutablePath);$(VS_ExecutablePath);$(MSBuild_ExecutablePath);$(FxCopDir);$(PATH)</ExecutablePath>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_ARM64);$(WindowsSDK_LibraryPath_ARM64);$(SolutionDir)includes\libarm64;</LibraryPath>
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)files\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(SolutionDir)includes\lib64;</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x86);$(WindowsSDK_LibraryPath_x86);$(SolutionDir)includes\lib32;</LibraryPath>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)includes;</IncludePath>
    <LibraryPath>$(VC_LibraryPath_ARM64);$(WindowsSDK_LibraryPath_ARM64);$(SolutionDir)includes\libarm64;</LibraryPath>
    <OutDir>$(SolutionDir)files\$(PlatformShortName)\</OutDir>
    <IntDir>$(SolutionDir)files\temp\$(ProjectName)\$(PlatformShortName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;VMM_STATIC;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalOptions>/DSQLITE_THREADSAFE=2 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>vmmdll.def</ModuleDefinitionFile>
      <ProgramDatabaseFile>$(OutDir)\lib\$(TargetName).pdb</ProgramDatabaseFile>
      <ImportLibrary>$(OutDir)lib\$(TargetName).lib</ImportLibrary>
      <AdditionalDependencies>leechcore.lib;bcrypt.lib;crypt32.lib;Shlwapi.lib;Ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(OutDir)\lib\vmm.lib" "$(SolutionDir)includes\lib64" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>copy "$(ProjectDir)\vmmdll.h" "$(SolutionDir)\includes\" /y</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;VMM_STATIC;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalOptions>/DSQLITE_THREADSAFE=2 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy "$(OutDir)\vmm.lib" "$(SolutionDir)includes\lib32" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>copy "$(ProjectDir)\vmmdll.h" "$(SolutionDir)\includes\" /y</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;VMM_STATIC;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <AdditionalOptions>/DSQLITE_THREADSAFE=2 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy "$(OutDir)\vmm.lib" "$(SolutionDir)includes\libarm64\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>copy "$(ProjectDir)\vmmdll.h" "$(SolutionDir)\includes\" /y</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;VMM_STATIC;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalOptions>/DSQLITE_THREADSAFE=2 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard>Default</LanguageStandard>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy "$(OutDir)\vmm.lib" "$(SolutionDir)includes\lib64" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>copy "$(ProjectDir)\vmmdll.h" "$(SolutionDir)\includes\" /y</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;VMM_STATIC;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <AdditionalOptions>/DSQLITE_THREADSAFE=2 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy "$(OutDir)\vmm.lib" "$(SolutionDir)includes\lib32" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>copy "$(ProjectDir)\vmmdll.h" "$(SolutionDir)\includes\" /y</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;VMM_STATIC;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAs>CompileAsC</CompileAs>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <AdditionalOptions>/DSQLITE_THREADSAFE=2 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <PostBuildEvent>
      <Command>copy "$(OutDir)\vmm.lib" "$(SolutionDir)includes\libarm64\" /y
</Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>copy "$(ProjectDir)\vmmdll.h" "$(SolutionDir)\includes\" /y</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\includes\leechcore.h" />
    <ClInclude Include="..\includes\libpdbcrust.h" />
    <ClInclude Include="..\includes\vmmyara.h" />
    <ClInclude Include="charutil.h" />
    <ClInclude Include="ext\lz4.h" />
    <ClInclude Include="ext\miniz.h" />
    <ClInclude Include="ext\sha256.h" />
    <ClInclude Include="ext\sqlite3.h" />
    <ClInclude Include="ext\sqlite3ext.h" />
    <ClInclude Include="fc.h" />
    <ClInclude Include="infodb.h" />
    <ClInclude Include="mm\mm.h" />
    <ClInclude Include="mm\mm_pfn.h" />
    <ClInclude Include="modules\modules.h" />
    <ClInclude Include="modules\modules_init.h" />
    <ClInclude Include="ob\ob.h" />
    <ClInclude Include="ob\ob_tag.h" />
    <ClInclude Include="oscompatibility.h" />
    <ClInclude Include="pdb.h" />
    <ClInclude Include="pe.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="statistics.h" />
    <ClInclude Include="sysquery.h" />
    <ClInclude Include="util.h" />
    <ClInclude Include="pluginmanager.h" />
    <ClInclude Include="version.h" />
    <ClInclude Include="vmm.h" />
    <ClInclude Include="vmmdll.h" />
    <ClInclude Include="vmmdll_core.h" />
    <ClInclude Include="vmmdll_remote.h" />
    <ClInclude Include="vmmex.h" />
    <ClInclude Include="vmmheap.h" />
    <ClInclude Include="vmmlog.h" />
    <ClInclude Include="vmmnet.h" />
    <ClInclude Include="vmmproc.h" />
    <ClInclude Include="vmmuserconfig.h" />
    <ClInclude Include="vmmvm.h" />
    <ClInclude Include="vmmwin.h" />
    <ClInclude Include="vmmwindef.h" />
    <ClInclude Include="vmmwininit.h" />
    <ClInclude Include="vmmwinobj.h" />
    <ClInclude Include="vmmwinpool.h" />
    <ClInclude Include="vmmwinreg.h" />
    <ClInclude Include="vmmwinsvc.h" />
    <ClInclude Include="vmmwinthread.h" />
    <ClInclude Include="vmmwork.h" />
    <ClInclude Include="vmmyarautil.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="charutil.c" />
    <ClCompile Include="ext\lz4.c" />
    <ClCompile Include="ext\miniz.c" />
    <ClCompile Include="ext\sha256.c" />
    <ClCompile Include="ext\sqlite3.c" />
    <ClCompile Include="fc.c" />
    <ClCompile Include="infodb.c" />
    <ClCompile Include="mm\mm_arm64.c" />
    <ClCompile Include="mm\mm_pfn.c" />
    <ClCompile Include="mm\mm_vad.c" />
    <ClCompile Include="mm\mm_win.c" />
    <ClCompile Include="mm\mm_x64.c" />
    <ClCompile Include="mm\mm_x86.c" />
    <ClCompile Include="mm\mm_x86pae.c" />
    <ClCompile Include="modules\m_conf.c" />
    <ClCompile Include="modules\m_evil_apc1.c" />
    <ClCompile Include="modules\m_evil_av1.c" />
    <ClCompile Include="modules\m_evil_entropy.c" />
    <ClCompile Include="modules\m_evil_kern1.c" />
    <ClCompile Include="modules\m_evil_kernproc1.c" />
    <ClCompile Include="modules\m_evil_proc1.c" />
    <ClCompile Include="modules\m_evil_proc2.c" />
    <ClCompile Include="modules\m_evil_proc3.c" />
    <ClCompile Include="modules\m_evil_thread1.c" />
    <ClCompile Include="modules\m_fc_csv.c" />
    <ClCompile Include="modules\m_fc_file.c" />
    <ClCompile Include="modules\m_fc_findevil.c" />
    <ClCompile Include="modules\m_fc_handle.c" />
    <ClCompile Include="modules\m_fc_json.c" />
    <ClCompile Include="modules\m_fc_module.c" />
    <ClCompile Include="modules\m_fc_ntfs.c" />
    <ClCompile Include="modules\m_fc_prefetch.c" />
    <ClCompile Include="modules\m_fc_proc.c" />
    <ClCompile Include="modules\m_fc_registry.c" />
    <ClCompile Include="modules\m_fc_sys.c" />
    <ClCompile Include="modules\m_fc_thread.c" />
    <ClCompile Include="modules\m_fc_timeline.c" />
    <ClCompile Include="modules\m_fc_web.c" />
    <ClCompile Include="modules\m_fc_yara.c" />
    <ClCompile Include="modules\m_misc_bitlocker.c" />
    <ClCompile Include="modules\m_misc_eventlog.c" />
    <ClCompile Include="modules\m_misc_procinfo.c" />
    <ClCompile Include="modules\m_misc_view.c" />
    <ClCompile Include="modules\m_phys2virt.c" />
    <ClCompile Include="modules\m_proc_console.c" />
    <ClCompile Include="modules\m_proc_file_handles_vads.c" />
    <ClCompile Include="modules\m_proc_file_modules.c" />
    <ClCompile Include="modules\m_proc_handle.c" />
    <ClCompile Include="modules\m_proc_heap.c" />
    <ClCompile Include="modules\m_proc_ldrmodules.c" />
    <ClCompile Include="modules\m_proc_memmap.c" />
    <ClCompile Include="modules\m_proc_minidump.c" />
    <ClCompile Include="modules\m_proc_thread.c" />
    <ClCompile Include="modules\m_proc_token.c" />
    <ClCompile Include="modules\m_proc_virt2phys.c" />
    <ClCompile Include="modules\m_search.c" />
    <ClCompile Include="modules\m_searchyara.c" />
    <ClCompile Include="modules\m_sys.c" />
    <ClCompile Include="modules\m_sys_cert.c" />
    <ClCompile Include="modules\m_sys_driver.c" />
    <ClCompile Include="modules\m_sys_mem.c" />
    <ClCompile Include="modules\m_sys_net.c" />
    <ClCompile Include="modules\m_sys_netdns.c" />
    <ClCompile Include="modules\m_sys_obj.c" />
    <ClCompile Include="modules\m_sys_pool.c" />
    <ClCompile Include="modules\m_sys_proc.c" />
    <ClCompile Include="modules\m_sys_svc.c" />
    <ClCompile Include="modules\m_sys_syscall.c" />
    <ClCompile Include="modules\m_sys_sysinfo.c" />
    <ClCompile Include="modules\m_sys_task.c" />
    <ClCompile Include="modules\m_sys_user.c" />
    <ClCompile Include="modules\m_vfsfc.c" />
    <ClCompile Include="modules\m_vfsproc.c" />
    <ClCompile Include="modules\m_vfsroot.c" />
    <ClCompile Include="modules\m_vm.c" />
    <ClCompile Include="modules\m_winreg.c" />
    <ClCompile Include="ob\ob_bytequeue.c" />
    <ClCompile Include="ob\ob_cachemap.c" />
    <ClCompile Include="ob\ob_compressed.c" />
    <ClCompile Include="ob\ob_container.c" />
    <ClCompile Include="ob\ob_core.c" />
    <ClCompile Include="ob\ob_counter.c" />
    <ClCompile Include="ob\ob_map.c" />
    <ClCompile Include="ob\ob_memfile.c" />
    <ClCompile Include="ob\ob_set.c" />
    <ClCompile Include="ob\ob_strmap.c" />
    <ClCompile Include="oscompatibility.c" />
    <ClCompile Include="pdb.c" />
    <ClCompile Include="pe.c" />
    <ClCompile Include="statistics.c" />
    <ClCompile Include="sysquery.c" />
    <ClCompile Include="vmm.c" />
    <ClCompile Include="vmmdll.c" />
    <ClCompile Include="vmmdll_core.c" />
    <ClCompile Include="vmmdll_remote.c" />
    <ClCompile Include="vmmdll_scatter.c" />
    <ClCompile Include="vmmex_light.c" />
    <ClCompile Include="vmmheap.c" />
    <ClCompile Include="vmmlog.c" />
    <ClCompile Include="vmmnet.c" />
    <ClCompile Include="vmmproc.c" />
    <ClCompile Include="vmmuserconfig.c" />
    <ClCompile Include="vmmvm.c" />
    <ClCompile Include="vmmwin.c" />
    <ClCompile Include="pluginmanager.c" />
    <ClCompile Include="vmmwininit.c" />
    <ClCompile Include="vmmwinobj.c" />
    <ClCompile Include="vmmwinpool.c" />
    <ClCompile Include="vmmwinreg.c" />
    <ClCompile Include="vmmwinsvc.c" />
    <ClCompile Include="vmmwinthread.c" />
    <ClCompile Include="vmmwork.c" />
    <ClCompile Include="vmmyarautil.c" />
    <ClCompile Include="vmmyarawrap.c" />
    <ClCompile Include="util.c" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\MemProcFS-dev\vmm\res\m_fc_json_elastic_import_unauth.ps1" />
    <None Include="Makefile" />
    <None Include="Makefile.macos" />
    <None Include="res\m_fc_json_elastic_import.ps1" />
    <None Include="vmmdll.def" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="vmm.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <PropertyGroup>
    <CAExcludePath>$(ProjectDir)\include;$(CAExcludePath);$(ProjectDir)\ext;$(CAExcludePath)</CAExcludePath>
  </PropertyGroup>
</Project>