{"format": 1, "restore": {"I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\example\\vmmsharp_example.csproj": {}}, "projects": {"I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\example\\vmmsharp_example.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\example\\vmmsharp_example.csproj", "projectName": "vmmsharp_example", "projectPath": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\example\\vmmsharp_example.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\example\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\vmmsharp\\vmmsharp.csproj": {"projectPath": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\vmmsharp\\vmmsharp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\vmmsharp\\vmmsharp.csproj": {"version": "5.14.10", "restore": {"projectUniqueName": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\vmmsharp\\vmmsharp.csproj", "projectName": "Vmmsharp", "projectPath": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\vmmsharp\\vmmsharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "I:\\Cheat\\MemProcFS-master\\MemProcFS\\vmmsharp\\vmmsharp\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48", "net5.0", "net6.0", "net7.0", "net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}, "net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net7.0": {"targetAlias": "net7.0", "projectReferences": {}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[5.0.0, 5.0.0]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[5.0.0, 5.0.0]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[5.0.0, 5.0.0]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net48": {"targetAlias": "net48", "dependencies": {"Microsoft.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "System.Data.DataSetExtensions": {"target": "Package", "version": "[4.5.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}