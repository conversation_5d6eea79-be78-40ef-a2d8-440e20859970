CC=clang
CFLAGS += -I. -I../includes -D MACOS -D _GNU_SOURCE -D SQLITE_THREADSAFE=2 -D SQLITE_CORE -fvisibility=hidden -pthread
CFLAGS += -fPIC -fstack-protector-strong -D_FORTIFY_SOURCE=2 -O1
CFLAGS += -Wall -Wno-multichar -Wno-unused-result -Wno-unused-variable -Wno-unused-value -Wno-pointer-sign
CFLAGS += -Wno-pointer-to-int-cast -Wno-int-to-pointer-cast
CFLAGS += -mmacosx-version-min=11.0
# DEBUG FLAGS BELOW
#CFLAGS += -O0
#CFLAGS += -fsanitize=address
# DEBUG FLAGS ABOVE
LDFLAGS += -dynamiclib -L. ./leechcore.dylib -lm
LDFLAGS += -Wl,-rpath,@loader_path
LDFLAGS += -g -mmacosx-version-min=11.0

DEPS = vmmdll.h
OBJ = oscompatibility.o charutil.o util.o pe.o vmmdll.o vmmdll_core.o        \
	  vmmdll_remote.o vmmdll_scatter.o vmm.o vmmex_light.o fc.o              \
	  mm/mm_arm64.o mm/mm_x64.o mm/mm_x86.o mm/mm_x86pae.o mm/mm_pfn.o       \
	  mm/mm_vad.o mm/mm_win.o                                                \
	  pluginmanager.o pdb.o infodb.o                                         \
	  ext/lz4.o ext/miniz.o ext/sha256.o ext/sqlite3.o                       \
	  ob/ob_bytequeue.o ob/ob_cachemap.o ob/ob_compressed.o                  \
	  ob/ob_container.o ob/ob_core.o ob/ob_counter.o ob/ob_map.o             \
	  ob/ob_memfile.o ob/ob_set.o ob/ob_strmap.o                             \
	  statistics.o sysquery.o vmmheap.o vmmlog.o vmmnet.o                    \
	  vmmproc.o vmmvm.o vmmwininit.o vmmwin.o vmmwinobj.o vmmwinpool.o       \
	  vmmwinreg.o vmmwinsvc.o vmmwinthread.o vmmuserconfig.o vmmwork.o       \
	  vmmyarautil.o vmmyarawrap.o                                            \
	  modules/m_vfsroot.o modules/m_vfsproc.o modules/m_vfsfc.o              \
	  modules/m_conf.o modules/m_vm.o modules/m_winreg.o                     \
	  modules/m_fc_csv.o modules/m_fc_file.o modules/m_fc_findevil.o         \
	  modules/m_fc_handle.o modules/m_fc_json.o modules/m_fc_module.o        \
	  modules/m_fc_ntfs.o modules/m_fc_prefetch.o modules/m_fc_proc.o        \
	  modules/m_fc_registry.o modules/m_fc_sys.o modules/m_fc_thread.o       \
	  modules/m_fc_timeline.o modules/m_fc_web.o modules/m_fc_yara.o         \
	  modules/m_evil_apc1.o modules/m_evil_av1.o modules/m_evil_entropy.o    \
	  modules/m_evil_kern1.o modules/m_evil_kernproc1.o                      \
	  modules/m_evil_proc1.o modules/m_evil_proc2.o modules/m_evil_proc3.o   \
	  modules/m_evil_thread1.o                                               \
	  modules/m_misc_bitlocker.o modules/m_misc_eventlog.o                   \
	  modules/m_misc_procinfo.o modules/m_misc_view.o                        \
	  modules/m_sys.o modules/m_sys_driver.o modules/m_sys_mem.o             \
	  modules/m_sys_net.o modules/m_sys_netdns.o modules/m_sys_obj.o         \
	  modules/m_sys_pool.o modules/m_sys_proc.o modules/m_sys_svc.o          \
	  modules/m_sys_syscall.o modules/m_sys_sysinfo.o modules/m_sys_task.o   \
	  modules/m_sys_user.o                                                   \
	  modules/m_phys2virt.o modules/m_search.o modules/m_searchyara.o        \
	  modules/m_proc_console.o modules/m_proc_file_handles_vads.o            \
	  modules/m_proc_file_modules.o modules/m_proc_handle.o                  \
	  modules/m_proc_heap.o modules/m_proc_ldrmodules.o                      \
	  modules/m_proc_memmap.o modules/m_proc_minidump.o                      \
	  modules/m_proc_thread.o modules/m_proc_token.o                         \
	  modules/m_proc_virt2phys.o

# ARCH SPECIFIC FLAGS:
CFLAGS_X86_64  = $(CFLAGS) -arch x86_64
CFLAGS_ARM64   = $(CFLAGS) -arch arm64
LDFLAGS_X86_64 = $(LDFLAGS) -arch x86_64
LDFLAGS_ARM64  = $(LDFLAGS) -arch arm64
OBJ_X86_64 = $(OBJ:.o=.o.x86_64)
OBJ_ARM64  = $(OBJ:.o=.o.arm64)

all: vmm.dylib

%.o.x86_64: %.c $(DEPS)
	$(CC) $(CFLAGS_X86_64) -c -o $@ $<

%.o.arm64: %.c $(DEPS)
	$(CC) $(CFLAGS_ARM64) -c -o $@ $<

vmm_x86_64.dylib: $(OBJ_X86_64)
	cp ../files/leechcore.dylib . || cp ../../LeechCore*/files/leechcore.dylib . || true
	$(CC) $(LDFLAGS_X86_64) -o $@ $^

vmm_arm64.dylib: $(OBJ_ARM64)
	cp ../files/leechcore.dylib . || cp ../../LeechCore*/files/leechcore.dylib . || true
	$(CC) $(LDFLAGS_ARM64) -o $@ $^

vmm.dylib: vmm_x86_64.dylib vmm_arm64.dylib
	lipo -create -output vmm.dylib vmm_x86_64.dylib vmm_arm64.dylib
	install_name_tool -id @rpath/vmm.dylib vmm.dylib
	mv vmm.dylib ../files/
	mv leechcore.dylib ../files/
	rm -f *.o *.o.x86_64 *.o.arm64 || true
	rm -f */*.o */*.o.x86_64 */*.o.arm64 || true
	rm -f *.dylib || true
	true

clean:
	rm -f *.o *.o.x86_64 *.o.arm64 || true
	rm -f */*.o */*.o.x86_64 */*.o.arm64 || true
	rm -f *.dylib || true
