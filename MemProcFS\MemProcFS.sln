﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32505.173
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "memprocfs", "memprocfs\memprocfs.vcxproj", "{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}"
	ProjectSection(ProjectDependencies) = postProject
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A} = {3476ABD2-5DEA-43E6-A676-8BE25F74535A}
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B} = {6326FCE0-1BA5-4AEC-9973-7783309FFD6B}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "vmm", "vmm\vmm.vcxproj", "{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "m_vmemd", "m_vmemd\m_vmemd.vcxproj", "{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}"
	ProjectSection(ProjectDependencies) = postProject
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B} = {6326FCE0-1BA5-4AEC-9973-7783309FFD6B}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "python", "python", "{50E46B01-0786-4222-B8C8-8D1612DA8A3B}"
	ProjectSection(SolutionItems) = preProject
		files\memprocfs.py = files\memprocfs.py
		files\memprocfs_example.py = files\memprocfs_example.py
		files\memprocfs_pythonexec_example.py = files\memprocfs_pythonexec_example.py
		files\vmmpyplugin.py = files\vmmpyplugin.py
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "vmmpyc", "vmmpyc\vmmpyc.vcxproj", "{9E47796D-B834-470E-B437-0754BC14DF09}"
	ProjectSection(ProjectDependencies) = postProject
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B} = {6326FCE0-1BA5-4AEC-9973-7783309FFD6B}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "vmm_example", "vmm_example\vmm_example.vcxproj", "{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}"
	ProjectSection(ProjectDependencies) = postProject
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B} = {6326FCE0-1BA5-4AEC-9973-7783309FFD6B}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "plugins.pym_procstruct", "plugins.pym_procstruct", "{7BEEEE90-F2CC-4ADD-BA8B-82599E3D1408}"
	ProjectSection(SolutionItems) = preProject
		files\plugins\pym_procstruct\pym_procstruct.py = files\plugins\pym_procstruct\pym_procstruct.py
		files\plugins\pym_procstruct\__init__.py = files\plugins\pym_procstruct\__init__.py
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "dissect.cstruct", "dissect.cstruct", "{A8B68178-FE06-42E2-80C9-2C936973B5FA}"
	ProjectSection(SolutionItems) = preProject
		files\dissect\cstruct\cstruct.py = files\dissect\cstruct\cstruct.py
		files\dissect\cstruct\__init__.py = files\dissect\cstruct\__init__.py
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "leechcore", "..\LeechCore\leechcore\leechcore.vcxproj", "{3476ABD2-5DEA-43E6-A676-8BE25F74535A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "vmmjava", "vmmjava", "{1390A2F3-1ECE-4A66-8B60-6FDCB599BE63}"
	ProjectSection(SolutionItems) = preProject
		vmmjava\VmmExample.java = vmmjava\VmmExample.java
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "vmm", "vmm", "{F64725D0-147F-43D1-95AE-FDE1E8C1EDCD}"
	ProjectSection(SolutionItems) = preProject
		vmmjava\vmm\IVmm.java = vmmjava\vmm\IVmm.java
		vmmjava\vmm\IVmmMemScatterMemory.java = vmmjava\vmm\IVmmMemScatterMemory.java
		vmmjava\vmm\IVmmModule.java = vmmjava\vmm\IVmmModule.java
		vmmjava\vmm\IVmmPdb.java = vmmjava\vmm\IVmmPdb.java
		vmmjava\vmm\IVmmProcess.java = vmmjava\vmm\IVmmProcess.java
		vmmjava\vmm\IVmmRegHive.java = vmmjava\vmm\IVmmRegHive.java
		vmmjava\vmm\IVmmRegKey.java = vmmjava\vmm\IVmmRegKey.java
		vmmjava\vmm\IVmmRegValue.java = vmmjava\vmm\IVmmRegValue.java
		vmmjava\vmm\VmmException.java = vmmjava\vmm\VmmException.java
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "entry", "entry", "{D65E9BD1-4BB8-4418-BC41-82884843D377}"
	ProjectSection(SolutionItems) = preProject
		vmmjava\vmm\entry\VmmMap_HandleEntry.java = vmmjava\vmm\entry\VmmMap_HandleEntry.java
		vmmjava\vmm\entry\VmmMap_HeapAllocEntry.java = vmmjava\vmm\entry\VmmMap_HeapAllocEntry.java
		vmmjava\vmm\entry\VmmMap_HeapEntry.java = vmmjava\vmm\entry\VmmMap_HeapEntry.java
		vmmjava\vmm\entry\VmmMap_HeapMap.java = vmmjava\vmm\entry\VmmMap_HeapMap.java
		vmmjava\vmm\entry\VmmMap_HeapSegmentEntry.java = vmmjava\vmm\entry\VmmMap_HeapSegmentEntry.java
		vmmjava\vmm\entry\VmmMap_MemMapEntry.java = vmmjava\vmm\entry\VmmMap_MemMapEntry.java
		vmmjava\vmm\entry\VmmMap_ModuleDataDirectory.java = vmmjava\vmm\entry\VmmMap_ModuleDataDirectory.java
		vmmjava\vmm\entry\VmmMap_ModuleExport.java = vmmjava\vmm\entry\VmmMap_ModuleExport.java
		vmmjava\vmm\entry\VmmMap_ModuleImport.java = vmmjava\vmm\entry\VmmMap_ModuleImport.java
		vmmjava\vmm\entry\VmmMap_ModuleSection.java = vmmjava\vmm\entry\VmmMap_ModuleSection.java
		vmmjava\vmm\entry\VmmMap_NetEntry.java = vmmjava\vmm\entry\VmmMap_NetEntry.java
		vmmjava\vmm\entry\VmmMap_PoolEntry.java = vmmjava\vmm\entry\VmmMap_PoolEntry.java
		vmmjava\vmm\entry\VmmMap_PoolMap.java = vmmjava\vmm\entry\VmmMap_PoolMap.java
		vmmjava\vmm\entry\VmmMap_PteEntry.java = vmmjava\vmm\entry\VmmMap_PteEntry.java
		vmmjava\vmm\entry\VmmMap_ServiceEntry.java = vmmjava\vmm\entry\VmmMap_ServiceEntry.java
		vmmjava\vmm\entry\VmmMap_ThreadEntry.java = vmmjava\vmm\entry\VmmMap_ThreadEntry.java
		vmmjava\vmm\entry\VmmMap_UnloadedModuleEntry.java = vmmjava\vmm\entry\VmmMap_UnloadedModuleEntry.java
		vmmjava\vmm\entry\VmmMap_UserEntry.java = vmmjava\vmm\entry\VmmMap_UserEntry.java
		vmmjava\vmm\entry\VmmMap_VadEntry.java = vmmjava\vmm\entry\VmmMap_VadEntry.java
		vmmjava\vmm\entry\VmmMap_VadExEntry.java = vmmjava\vmm\entry\VmmMap_VadExEntry.java
		vmmjava\vmm\entry\Vmm_VfsListEntry.java = vmmjava\vmm\entry\Vmm_VfsListEntry.java
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "internal", "internal", "{A8744524-49F8-4CAA-BD85-7C1EACA1164F}"
	ProjectSection(SolutionItems) = preProject
		vmmjava\vmm\internal\VmmImpl.java = vmmjava\vmm\internal\VmmImpl.java
		vmmjava\vmm\internal\VmmNative.java = vmmjava\vmm\internal\VmmNative.java
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{99570206-7FB9-41C4-87C9-69064B8069FA}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "vmmrust", "vmmrust", "{CA129E44-E6CB-4730-8CE0-B45894B01177}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "m_example_plugin", "m_example_plugin", "{0CA8FB8A-9763-4130-ADBD-C67EFF4897C7}"
	ProjectSection(SolutionItems) = preProject
		vmmrust\m_example_plugin\Cargo.toml = vmmrust\m_example_plugin\Cargo.toml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "memprocfs", "memprocfs", "{626E4023-93CC-4D9B-A557-905FF349777F}"
	ProjectSection(SolutionItems) = preProject
		vmmrust\memprocfs\Cargo.toml = vmmrust\memprocfs\Cargo.toml
		vmmrust\memprocfs\README.md = vmmrust\memprocfs\README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "memprocfs_example", "memprocfs_example", "{30C76595-D55E-452F-A9C6-EFC23ADC36DF}"
	ProjectSection(SolutionItems) = preProject
		vmmrust\memprocfs_example\Cargo.toml = vmmrust\memprocfs_example\Cargo.toml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{F634AD49-3F87-4AF4-AA2B-6F0F3560C210}"
	ProjectSection(SolutionItems) = preProject
		vmmrust\memprocfs_example\src\main.rs = vmmrust\memprocfs_example\src\main.rs
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{F36BD36B-329E-4A24-8522-FB31442439E6}"
	ProjectSection(SolutionItems) = preProject
		vmmrust\memprocfs\src\lib_memprocfs.rs = vmmrust\memprocfs\src\lib_memprocfs.rs
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{FDE47FDC-7D70-41DB-A2E9-3CC2D0B16FF0}"
	ProjectSection(SolutionItems) = preProject
		vmmrust\m_example_plugin\src\lib.rs = vmmrust\m_example_plugin\src\lib.rs
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "vmmsharp", "vmmsharp\vmmsharp\vmmsharp.csproj", "{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "vmmsharp_example", "vmmsharp\example\vmmsharp_example.csproj", "{5A727016-07EA-421E-81B9-8CDC0B525E1C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|Any CPU.ActiveCfg = Debug|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|Any CPU.Build.0 = Debug|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|ARM64.Build.0 = Debug|ARM64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|x64.ActiveCfg = Debug|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|x64.Build.0 = Debug|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|x86.ActiveCfg = Debug|Win32
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Debug|x86.Build.0 = Debug|Win32
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|Any CPU.ActiveCfg = Release|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|Any CPU.Build.0 = Release|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|ARM64.ActiveCfg = Release|ARM64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|ARM64.Build.0 = Release|ARM64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|x64.ActiveCfg = Release|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|x64.Build.0 = Release|x64
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|x86.ActiveCfg = Release|Win32
		{A9CE6DD1-A834-4FFD-A4C2-50D9D2F14BFD}.Release|x86.Build.0 = Release|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|Any CPU.ActiveCfg = Debug|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|Any CPU.Build.0 = Debug|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|ARM64.Build.0 = Debug|ARM64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x64.ActiveCfg = Debug|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x64.Build.0 = Debug|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x86.ActiveCfg = Debug|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Debug|x86.Build.0 = Debug|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|Any CPU.ActiveCfg = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|Any CPU.Build.0 = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|ARM64.ActiveCfg = Release|ARM64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|ARM64.Build.0 = Release|ARM64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x64.ActiveCfg = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x64.Build.0 = Release|x64
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x86.ActiveCfg = Release|Win32
		{6326FCE0-1BA5-4AEC-9973-7783309FFD6B}.Release|x86.Build.0 = Release|Win32
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|Any CPU.ActiveCfg = Debug|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|Any CPU.Build.0 = Debug|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|ARM64.Build.0 = Debug|ARM64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|x64.ActiveCfg = Debug|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|x64.Build.0 = Debug|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|x86.ActiveCfg = Debug|Win32
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Debug|x86.Build.0 = Debug|Win32
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|Any CPU.ActiveCfg = Release|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|Any CPU.Build.0 = Release|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|ARM64.ActiveCfg = Release|ARM64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|ARM64.Build.0 = Release|ARM64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|x64.ActiveCfg = Release|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|x64.Build.0 = Release|x64
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|x86.ActiveCfg = Release|Win32
		{BC6D11FF-3B1E-480E-A1AB-AAE5868FE9B3}.Release|x86.Build.0 = Release|Win32
		{9E47796D-B834-470E-B437-0754BC14DF09}.Debug|Any CPU.ActiveCfg = Debug|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Debug|Any CPU.Build.0 = Debug|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Debug|ARM64.ActiveCfg = Debug|Win32
		{9E47796D-B834-470E-B437-0754BC14DF09}.Debug|x64.ActiveCfg = Debug|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Debug|x64.Build.0 = Debug|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Debug|x86.ActiveCfg = Debug|Win32
		{9E47796D-B834-470E-B437-0754BC14DF09}.Release|Any CPU.ActiveCfg = Release|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Release|Any CPU.Build.0 = Release|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Release|ARM64.ActiveCfg = Release|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Release|x64.ActiveCfg = Release|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Release|x64.Build.0 = Release|x64
		{9E47796D-B834-470E-B437-0754BC14DF09}.Release|x86.ActiveCfg = Release|Win32
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|Any CPU.ActiveCfg = Debug|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|Any CPU.Build.0 = Debug|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|ARM64.Build.0 = Debug|ARM64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|x64.ActiveCfg = Debug|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|x64.Build.0 = Debug|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|x86.ActiveCfg = Debug|Win32
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Debug|x86.Build.0 = Debug|Win32
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|Any CPU.ActiveCfg = Release|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|Any CPU.Build.0 = Release|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|ARM64.ActiveCfg = Release|ARM64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|ARM64.Build.0 = Release|ARM64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|x64.ActiveCfg = Release|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|x64.Build.0 = Release|x64
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|x86.ActiveCfg = Release|Win32
		{45CC506E-E97A-45B8-8050-B2C5BC8A4B15}.Release|x86.Build.0 = Release|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|Any CPU.ActiveCfg = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|Any CPU.Build.0 = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|ARM64.Build.0 = Debug|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x64.ActiveCfg = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x64.Build.0 = Debug|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x86.ActiveCfg = Debug|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Debug|x86.Build.0 = Debug|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|Any CPU.ActiveCfg = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|Any CPU.Build.0 = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|ARM64.ActiveCfg = Release|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|ARM64.Build.0 = Release|ARM64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x64.ActiveCfg = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x64.Build.0 = Release|x64
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x86.ActiveCfg = Release|Win32
		{3476ABD2-5DEA-43E6-A676-8BE25F74535A}.Release|x86.Build.0 = Release|Win32
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|Any CPU.ActiveCfg = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|Any CPU.Build.0 = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|ARM64.ActiveCfg = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|ARM64.Build.0 = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|x64.ActiveCfg = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|x64.Build.0 = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|x86.ActiveCfg = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Debug|x86.Build.0 = Debug|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|Any CPU.ActiveCfg = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|Any CPU.Build.0 = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|ARM64.ActiveCfg = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|ARM64.Build.0 = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|x64.ActiveCfg = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|x64.Build.0 = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|x86.ActiveCfg = Release|x64
		{2DBA887A-64A5-4437-A8BB-DEAA292AA0A2}.Release|x86.Build.0 = Release|x64
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|ARM64.Build.0 = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|x64.Build.0 = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Debug|x86.Build.0 = Debug|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|ARM64.ActiveCfg = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|ARM64.Build.0 = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|x64.ActiveCfg = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|x64.Build.0 = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|x86.ActiveCfg = Release|Any CPU
		{5A727016-07EA-421E-81B9-8CDC0B525E1C}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7BEEEE90-F2CC-4ADD-BA8B-82599E3D1408} = {50E46B01-0786-4222-B8C8-8D1612DA8A3B}
		{A8B68178-FE06-42E2-80C9-2C936973B5FA} = {50E46B01-0786-4222-B8C8-8D1612DA8A3B}
		{F64725D0-147F-43D1-95AE-FDE1E8C1EDCD} = {1390A2F3-1ECE-4A66-8B60-6FDCB599BE63}
		{D65E9BD1-4BB8-4418-BC41-82884843D377} = {F64725D0-147F-43D1-95AE-FDE1E8C1EDCD}
		{A8744524-49F8-4CAA-BD85-7C1EACA1164F} = {F64725D0-147F-43D1-95AE-FDE1E8C1EDCD}
		{0CA8FB8A-9763-4130-ADBD-C67EFF4897C7} = {CA129E44-E6CB-4730-8CE0-B45894B01177}
		{626E4023-93CC-4D9B-A557-905FF349777F} = {CA129E44-E6CB-4730-8CE0-B45894B01177}
		{30C76595-D55E-452F-A9C6-EFC23ADC36DF} = {CA129E44-E6CB-4730-8CE0-B45894B01177}
		{F634AD49-3F87-4AF4-AA2B-6F0F3560C210} = {30C76595-D55E-452F-A9C6-EFC23ADC36DF}
		{F36BD36B-329E-4A24-8522-FB31442439E6} = {626E4023-93CC-4D9B-A557-905FF349777F}
		{FDE47FDC-7D70-41DB-A2E9-3CC2D0B16FF0} = {0CA8FB8A-9763-4130-ADBD-C67EFF4897C7}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CE6C3AA7-477A-4329-B4E3-5F219ACF4063}
	EndGlobalSection
EndGlobal
